/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/index/index" |
       "/pages/about/about" |
       "/pages/activity/activity-detail" |
       "/pages/activity/activity-list" |
       "/pages/demo/race-detail" |
       "/pages/loading/index" |
       "/pages/login/index" |
       "/pages/login/information" |
       "/pages/login/realname" |
       "/pages/message/index" |
       "/pages/participated/index" |
       "/pages/shequ/detail" |
       "/pages/shequ/index" |
       "/pages/shequn/detail" |
       "/pages/shequn/form" |
       "/pages/shequn/index" |
       "/pages/user/index" |
       "/pages/users/agreement" |
       "/pages/users/edit-profile" |
       "/pages/users/education" |
       "/pages/users/profile-detail" |
       "/pages/users/profile" |
       "/pages/welcome/index" |
       "/pages/users/create_carpool/index" |
       "/pages/users/create_posted/index" |
       "/pages/users/create_room/index" |
       "/pages/users/event_calendar/index" |
       "/pages/users/my_activity/index" |
       "/pages/users/my_post/index" |
       "/pages/users/partner_details/index" |
       "/pages/users/team_details/index" |
       "/pages/users/user_info/index" |
       "/pages/users/vip_card/index" |
       "/pages/users/vip_card/user_card";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/index/index" | "/pages/participated/index" | "/pages/shequ/index" | "/pages/shequn/index" | "/pages/user/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
