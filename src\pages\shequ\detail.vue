<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '详情',
    enablePullDownRefresh: true,
    onReachBottomDistance: 100,
  },
}
</route>

<template>
  <view class="detail-wrapper">
    <!-- 帖子内容 -->
    <view class="post-content">
      <!-- 用户信息 -->
      <view class="user-info">
        <image class="user-avatar" :src="postDetail.user?.avatar" mode="aspectFill"></image>
        <view class="user-details">
          <text class="user-name">{{ postDetail.user?.nickname }}</text>
          <text class="post-time">{{ postDetail.add_time_date }}</text>
        </view>
      </view>
    </view>
    <view>
      <view class="post">
        <!-- 帖子文字内容 -->
        <view class="post-text">
          {{ postDetail.content }}
        </view>
        <!-- 帖子图片内容 -->
        <view class="images">
          <view class="images1" v-if="postDetail.images.length === 1">
            <block v-for="(image, index) in postDetail.images" :key="index">
              <view class="img__item">
                <image
                  @click="previewImage(postDetail.images, index)"
                  class="img"
                  mode="aspectFill"
                  :src="image"
                ></image>
              </view>
            </block>
          </view>
          <view class="images4" v-else-if="postDetail.images.length === 2">
            <block v-for="(image, index) in postDetail.images" :key="index">
              <view class="img__item">
                <image
                  @click="previewImage(postDetail.images, index)"
                  class="img"
                  mode="aspectFill"
                  :src="image"
                ></image>
              </view>
            </block>
          </view>
          <view class="images3" v-else-if="postDetail.images.length === 3">
            <block v-for="(image, index) in postDetail.images" :key="index">
              <view class="img__item" :class="'item' + index">
                <image
                  @click="previewImage(postDetail.images, index)"
                  class="img"
                  mode="aspectFill"
                  :src="image"
                ></image>
              </view>
            </block>
          </view>
          <view class="images4" v-else-if="postDetail.images.length === 4">
            <block v-for="(image, index) in postDetail.images" :key="index">
              <view class="img__item">
                <image
                  @click="previewImage(postDetail.images, index)"
                  class="img"
                  mode="aspectFill"
                  :src="image"
                ></image>
              </view>
            </block>
          </view>
          <view class="images5" v-else-if="postDetail.images.length === 5">
            <view class="images__top">
              <view class="images__top__left">
                <view class="img__item">
                  <image
                    @click="previewImage(postDetail.images, 0)"
                    class="img"
                    mode="aspectFill"
                    :src="postDetail.images[0]"
                  ></image>
                </view>
              </view>
              <view class="images__top__right">
                <view class="img__item">
                  <image
                    @click="previewImage(postDetail.images, 1)"
                    class="img"
                    mode="aspectFill"
                    :src="postDetail.images[1]"
                  ></image>
                </view>
                <view class="img__item">
                  <image
                    @click="previewImage(postDetail.images, 2)"
                    class="img"
                    mode="aspectFill"
                    :src="postDetail.images[2]"
                  ></image>
                </view>
              </view>
            </view>
            <view class="images__bottom">
              <view class="img__item">
                <image
                  @click="previewImage(postDetail.images, 3)"
                  class="img"
                  mode="aspectFill"
                  :src="postDetail.images[3]"
                ></image>
              </view>
              <view class="img__item">
                <image
                  @click="previewImage(postDetail.images, 4)"
                  class="img"
                  mode="aspectFill"
                  :src="postDetail.images[4]"
                ></image>
              </view>
            </view>
          </view>
          <view class="images6" v-else-if="postDetail.images.length >= 6">
            <view class="images__top">
              <view class="images__top__left">
                <view class="img__item">
                  <image
                    @click="previewImage(postDetail.images, 0)"
                    class="img"
                    mode="aspectFill"
                    :src="postDetail.images[0]"
                  ></image>
                </view>
              </view>
              <view class="images__top__right">
                <view class="img__item">
                  <image
                    @click="previewImage(postDetail.images, 1)"
                    class="img"
                    mode="aspectFill"
                    :src="postDetail.images[1]"
                  ></image>
                </view>
                <view class="img__item">
                  <image
                    @click="previewImage(postDetail.images, 2)"
                    class="img"
                    mode="aspectFill"
                    :src="postDetail.images[2]"
                  ></image>
                </view>
              </view>
            </view>
            <view class="images__bottom">
              <block v-for="(image, index) in sliceImages" :key="index">
                <view class="img__item">
                  <image
                    @click="previewImage(postDetail.images, index + 3)"
                    class="img"
                    mode="aspectFill"
                    :src="image"
                  ></image>
                </view>
              </block>
            </view>
          </view>
          <!-- <view class="images1" v-else>
          <view class="img__item">
            <image
              @click="previewImage(defaultImages, 1)"
              class="img"
              mode="aspectFill"
              :src="defaultImages[0]"
            ></image>
          </view>
        </view> -->
        </view>
      </view>
    </view>
    <!-- 评论列表 -->
    <view class="comments-section">
      <view class="comment-item" v-for="comment in commentList" :key="comment.id">
        <image class="comment-avatar" :src="comment.user?.avatar" mode="aspectFill"></image>
        <view class="comment-content">
          <view class="comment-header">
            <text class="comment-user">{{ comment.user?.nickname }}</text>
            <text class="comment-time">{{ comment.add_time_date }}</text>
          </view>
          <text class="comment-text">{{ comment.comment }}</text>
        </view>
      </view>
    </view>
    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <button class="action-item" @click="handleShare">
        <text class="action-text">分享</text>
      </button>
      <button class="action-item" @click="handleContact">
        <text class="action-text">联系楼主</text>
      </button>
      <button class="action-item primary" @click="handlePost">
        <text class="action-text">我要发帖</text>
      </button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { activityDetail, commentListArticle } from '@/service/user/index'
import { useToast } from 'wot-design-uni'

const toast = useToast()

// 帖子详情数据
const postDetail = ref({
  id: '',
  title: '',
  content: '',
  images: [
    'https://dummyimage.com/300x300/ff6b9d/fff',
    'https://dummyimage.com/300x300/4ecdc4/fff',
    'https://dummyimage.com/300x300/45b7d1/fff',
    // 'https://dummyimage.com/300x300/96ceb4/fff',
    // 'https://dummyimage.com/300x300/feca57/fff',
    // 'https://dummyimage.com/300x300/ff9ff3/fff',
  ],
  user: {
    nickname: '美丽人生',
    avatar: '/static/images/login-female-default.png',
  },
  add_time_date: '发布帖子 5',
})

const sliceImages = [
  'https://dummyimage.com/300x300/ff6b9d/fff',
  'https://dummyimage.com/300x300/4ecdc4/fff',
  'https://dummyimage.com/300x300/45b7d1/fff',
  'https://dummyimage.com/300x300/96ceb4/fff',
  'https://dummyimage.com/300x300/feca57/fff',
  'https://dummyimage.com/300x300/ff9ff3/fff',
]

// 评论列表数据
const commentList = ref([
  {
    id: 1,
    user: {
      nickname: '176****6070',
      avatar: '/static/images/login-female-default.png',
    },
    comment: '姐妹真是完美的我来表现啊！肌肉线条和小细腿真的很有魅力！谢谢',
    add_time_date: '7月7日 12:00:00',
  },
  {
    id: 2,
    user: {
      nickname: '666',
      avatar: '/static/images/login-male-default.png',
    },
    comment: '好看，阳光',
    add_time_date: '7月7日 12:00:00',
  },
  {
    id: 3,
    user: {
      nickname: '888',
      avatar: '/static/images/login-female-default.png',
    },
    comment:
      '@176****6070："姐妹真是完美的我来表现啊！肌肉线条和小细腿真的很有魅力！谢谢"，谢谢，没有这么夸张啦，全靠',
    add_time_date: '7月7日 12:00:00',
  },
])

// 分页数据
const pageData = ref({
  page: 1,
  limit: 10,
  loading: false,
  loadend: false,
})

const postId = ref('')

// 预览图片
const previewImage = (images, index) => {
  uni.previewImage({
    current: index,
    urls: images,
  })
}

// 分享
const handleShare = () => {
  uni.showShareMenu({
    withShareTicket: true,
  })
}

// 联系楼主
const handleContact = () => {
  toast.show('联系楼主功能开发中')
}

// 我要发帖
const handlePost = () => {
  uni.navigateTo({
    url: '/pages/users/create_posted/index',
  })
}

// 加载帖子详情
const loadPostDetail = async (id: string) => {
  try {
    const [res, err] = await activityDetail(id)
    if (res) {
      postDetail.value = res.data
      loadCommentList()
    }
  } catch (error) {
    console.error('加载帖子详情失败:', error)
  }
}

// 加载评论列表
const loadCommentList = async () => {
  if (pageData.value.loadend) return
  pageData.value.loading = true

  try {
    const [res, err] = await commentListArticle(postId.value, {
      page: pageData.value.page,
      limit: pageData.value.limit,
    })

    if (res) {
      const list = res.data.list
      const loadend = list.length < pageData.value.limit
      pageData.value.loadend = loadend
      pageData.value.page++

      if (pageData.value.page === 2) {
        commentList.value = list
      } else {
        commentList.value = [...commentList.value, ...list]
      }
    }
  } catch (error) {
    console.error('加载评论列表失败:', error)
  }

  pageData.value.loading = false
}

onLoad((options) => {
  if (options.id) {
    postId.value = options.id
    // loadPostDetail(options.id)
  }
})

// 下拉刷新
onPullDownRefresh(() => {
  // pageData.value.page = 1
  // pageData.value.loadend = false
  // commentList.value = []

  // if (postId.value) {
  //   loadPostDetail(postId.value)
  // }

  setTimeout(() => {
    uni.stopPullDownRefresh()
  }, 1000)
})

// 上拉加载更多
onReachBottom(() => {
  // loadCommentList()
})
</script>

<style>
page {
  background-color: #f5f5f5;
}
</style>

<style lang="scss" scoped>
.detail-wrapper {
  min-height: 100vh;
  padding-bottom: 120rpx;
  padding-top: 10rpx;
}

.post-content {
  margin-bottom: 10rpx;
  padding: 30rpx;
  background-color: #fff;
}

.user-info {
  display: flex;
  align-items: center;

  .user-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 20rpx;
  }

  .user-details {
    flex: 1;

    .user-name {
      display: block;
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 8rpx;
    }

    .post-time {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.post {
  padding: 50rpx 30rpx;
  background-color: white;
}
.post-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 30rpx;
  white-space: pre-wrap;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10rpx;

  .image-item {
    aspect-ratio: 1;
    border-radius: 12rpx;
    overflow: hidden;

    .post-image {
      width: 100%;
      height: 100%;
    }
  }
}

.comments-section {
  background-color: #fff;
  padding: 30rpx;

  .comment-item {
    display: flex;
    margin-bottom: 30rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .comment-avatar {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      margin-right: 20rpx;
      flex-shrink: 0;
    }

    .comment-content {
      flex: 1;

      .comment-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12rpx;

        .comment-user {
          font-size: 26rpx;
          font-weight: 500;
          color: #333;
        }

        .comment-time {
          font-size: 22rpx;
          color: #999;
        }
      }

      .comment-text {
        font-size: 26rpx;
        line-height: 1.5;
        color: #666;
      }
    }
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx 0;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  .action-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60rpx;
    margin: 0 10rpx;
    border: 1rpx solid #ddd;
    border-radius: 30rpx;
    background-color: #fff;

    &.primary {
      background-color: #c5f355;
      border-color: #c5f355;

      .action-text {
        color: #333;
        font-weight: 500;
      }
    }

    .action-text {
      font-size: 26rpx;
      color: #666;
    }
  }
}
.images {
  .images1 {
    .img__item {
      width: 100%;
      .img {
        width: 100%;
        height: 700rpx;
        border-radius: 8rpx;
      }
    }
  }

  .images3 {
    display: grid;
    grid-template-columns: 2fr 1fr; /* 左边大图，右边小图 */
    grid-template-rows: 1fr 1fr; /* 右侧分为两行 */
    column-gap: 10rpx;
    row-gap: 10rpx;

    .item0 {
      grid-column: 1;
      grid-row: 1 / 3; /* 跨越两行 */
      .img {
        width: 100%;
        height: 100%;
        min-height: 430rpx; /* 确保最小高度 */
        border-radius: 8rpx;
        object-fit: cover; /* 保持图片比例 */
      }
    }

    .item1 {
      grid-column: 2;
      grid-row: 1;
      .img {
        width: 100%;
        height: 210rpx;
        border-radius: 8rpx;
        object-fit: cover; /* 保持图片比例 */
      }
    }

    .item2 {
      grid-column: 2;
      grid-row: 2;
      .img {
        width: 100%;
        height: 210rpx;
        border-radius: 8rpx;
        object-fit: cover; /* 保持图片比例 */
      }
    }
  }
  .images4 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    column-gap: 10rpx;
    row-gap: 10rpx;
    .img__item {
      width: 100%;
      .img {
        width: 340rpx;
        height: 340rpx;
        border-radius: 8rpx;
      }
    }
  }
  .images5 {
    .images__top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10rpx;
      &__left {
        width: flex;
        .img__item {
          width: 100%;
          .img {
            width: 460rpx;
            height: 460rpx;
            border-radius: 8rpx;
          }
        }
      }
      &__right {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        margin-left: 10rpx;
        row-gap: 10rpx;
        .img__item {
          width: 100%;
          .img {
            width: 220rpx;
            height: 226rpx;
            border-radius: 8rpx;
          }
        }
      }
    }
    .images__bottom {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      column-gap: 10rpx;
      .img__item {
        width: 100%;
        .img {
          width: 100%;
          height: 300rpx;
          border-radius: 8rpx;
        }
      }
    }
  }
  .images6 {
    .images__top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10rpx;
      &__left {
        width: flex;
        .img__item {
          width: 100%;
          .img {
            width: 460rpx;
            height: 460rpx;
            border-radius: 8rpx;
          }
        }
      }
      &__right {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        margin-left: 10rpx;
        row-gap: 10rpx;
        .img__item {
          width: 100%;
          .img {
            width: 100%;
            height: 226rpx;
            border-radius: 8rpx;
          }
        }
      }
    }
    .images__bottom {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      column-gap: 10rpx;
      row-gap: 10rpx;
      .img__item {
        width: 100%;
        .img {
          width: 100%;
          height: 200rpx;
          border-radius: 8rpx;
        }
      }
    }
  }
}
</style>
