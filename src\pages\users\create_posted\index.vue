<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '发帖',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
  },
}
</route>

<template>
  <view class="create-post-container">
    <!-- 标题输入区域 -->
    <view class="title-section">
      <view class="title-label">标题</view>
      <view class="title-input-wrapper">
        <input
          type="text"
          v-model="formData.title"
          placeholder="请输入标题"
          class="title-input"
          maxlength="10"
        />
        <view class="char-count">
          <text class="text-[#333]">{{ formData.title.length }}</text>
          <text class="text-[#333]">/10</text>
        </view>
      </view>
    </view>

    <!-- 内容输入区域 -->
    <view class="content-section">
      <textarea
        v-model="formData.content"
        placeholder="说说你的想法或者提个问题..."
        class="content-textarea"
        maxlength="1000"
        auto-height
      />
      <view class="char-count">
        <text class="text-[#333]">{{ formData.content.length }}</text>
        <text class="text-[#D2353E]">/1000</text>
      </view>
    </view>

    <!-- 上传图片区域 -->
    <view class="upload-section">
      <view class="upload-label">上传图片</view>
      <view class="upload-grid">
        <view v-for="(image, index) in uploadedImages" :key="index" class="upload-item image-item">
          <image :src="image" class="uploaded-image" mode="aspectFill" />
          <view class="delete-btn" @click="removeImage(index)">
            <wd-icon name="close" size="16px" color="#fff" />
          </view>
        </view>
        <view v-if="uploadedImages.length < 9" class="upload-item add-item" @click="chooseImage">
          <wd-icon name="add" size="40px" color="#ccc" />
        </view>
      </view>
    </view>

    <!-- 发布按钮 -->
    <view class="submit-section">
      <button
        type="button"
        hover-class="button-hover"
        :disabled="!canSubmit"
        class="submit-btn"
        @click="handleSubmit"
      >
        发布
      </button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { useToast } from 'wot-design-uni'
import useUpload from '@/hooks/useUpload'

const toast = useToast()

// 表单数据
const formData = reactive({
  title: '',
  content: '',
})

// 上传的图片列表
const uploadedImages = ref<string[]>([])

// 上传回调
const uploadCallback = (url: string) => {
  uploadedImages.value.push(url)
}

const { runUploadFile } = useUpload({}, uploadCallback)

// 选择图片
const chooseImage = () => {
  const remainingCount = 9 - uploadedImages.value.length
  uni.chooseImage({
    count: remainingCount,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      const tempFilePaths = Array.isArray(res.tempFilePaths)
        ? res.tempFilePaths
        : [res.tempFilePaths]
      tempFilePaths.forEach((filePath: string) => {
        runUploadFile(filePath)
      })
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
      toast.error('选择图片失败')
    },
  })
}

// 删除图片
const removeImage = (index: number) => {
  uploadedImages.value.splice(index, 1)
}

// 是否可以提交
const canSubmit = computed(() => {
  return formData.title.trim().length > 0 && formData.content.trim().length > 0
})

// 提交表单
const handleSubmit = () => {
  if (!canSubmit.value) {
    toast.error('请填写标题和内容')
    return
  }

  const submitData = {
    title: formData.title.trim(),
    content: formData.content.trim(),
    images: uploadedImages.value,
  }

  console.log('提交数据:', submitData)
  toast.success('发布成功')

  // 这里可以调用提交接口
  // 提交成功后可以跳转或返回
  setTimeout(() => {
    uni.navigateBack()
  }, 1500)
}
</script>

<style lang="scss" scoped>
.create-post-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 150rpx; /* 为底部固定按钮留出空间 */
}

.title-section {
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .title-label {
    font-size: 30rpx;
    font-weight: 500;
    color: #333;
    margin-right: 20rpx;
  }

  .title-input-wrapper {
    position: relative;
    flex: 1;
    .title-input {
      width: 100%;
      font-size: 28rpx;
      color: #333;
      padding-right: 80rpx;
      line-height: 1.5;
    }

    .char-count {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      font-size: 24rpx;
      //   color: #f92560;
    }
  }
}

.content-section {
  position: relative;
  padding: 30rpx;
  background-color: #ffffff;

  .content-textarea {
    width: 100%;
    box-sizing: border-box;
    min-height: 300rpx;
    padding: 30rpx;
    font-size: 28rpx;
    color: #333;
    background-color: #f5f5f5;
    border-radius: 16rpx;
    border: none;
    outline: none;
    resize: none;
    line-height: 1.6;
  }

  .char-count {
    position: absolute;
    right: 50rpx;
    bottom: 50rpx;
    font-size: 24rpx;
    color: #f92560;
  }
}

.upload-section {
  padding: 30rpx;
  background-color: #fff;
  margin-top: 20rpx;

  .upload-label {
    font-size: 30rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 30rpx;
  }

  .upload-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
  }

  .upload-item {
    position: relative;
    width: 200rpx;
    height: 200rpx;
    border-radius: 16rpx;
    overflow: hidden;

    &.image-item {
      .uploaded-image {
        width: 100%;
        height: 100%;
      }

      .delete-btn {
        position: absolute;
        top: 10rpx;
        right: 10rpx;
        width: 40rpx;
        height: 40rpx;
        background-color: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    &.add-item {
      background-color: #f5f5f5;
      border: 2rpx dashed #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.submit-section {
  padding: 30rpx;
  background-color: #f5f5f5;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 40rpx;

  .submit-btn {
    width: 100%;
    height: 88rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #fff;
    background: #c5f355;
    border: none;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    &[disabled] {
      color: #fff;
      background: #c5f355;
    }

    &:not([disabled]):active {
      opacity: 0.8;
    }
  }
}

.button-hover {
  opacity: 0.8;
}
</style>
