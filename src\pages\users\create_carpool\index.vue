<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '拼车搭子',
  },
}
</route>

<template>
  <view class="carpool-container">
    <view class="page-container">
      <!-- 表单区域 -->
      <view class="form-section">
        <!-- 赛事选择 -->
        <view class="form-item">
          <wd-picker
            v-model="formData.event"
            :columns="eventOptions"
            :use-default-slot="true"
            @confirm="handleEventConfirm"
          >
            <view class="form-field">
              <view class="field-label">*赛事</view>
              <view class="field-content">
                <text class="field-value">
                  {{ formData.event || '请选择报名赛事' }}
                </text>
                <wd-icon name="arrow-right" size="16px" color="#CCCCCC"></wd-icon>
              </view>
            </view>
          </wd-picker>
        </view>

        <!-- 拼车类型选择 -->
        <view class="form-item">
          <view class="form-field">
            <view class="field-label">*拼车</view>
            <view class="field-content">
              <view class="toggle-buttons">
                <button
                  class="toggle-btn"
                  :class="{ active: formData.carpoolType === 'find_car' }"
                  @click="formData.carpoolType = 'find_car'"
                >
                  人找车
                </button>
                <button
                  class="toggle-btn"
                  :class="{ active: formData.carpoolType === 'find_people' }"
                  @click="formData.carpoolType = 'find_people'"
                >
                  车找人
                </button>
              </view>
            </view>
          </view>
        </view>

        <!-- 出发地址 -->
        <view class="form-item">
          <view class="form-field">
            <view class="field-label">*出发地址</view>
            <view class="field-content">
              <input
                type="text"
                v-model="formData.startAddress"
                placeholder="请输入出发地址"
                class="field-input"
              />
              <button class="location-btn" @click="handleLocationSelect">
                <wd-icon name="location" size="16px" color="#666"></wd-icon>
                定位
              </button>
            </view>
          </view>
        </view>

        <!-- 人数选择 -->
        <view class="form-item">
          <view class="form-field">
            <view class="field-label">*人数</view>
            <view class="field-content">
              <input
                type="number"
                v-model="formData.peopleCount"
                placeholder="请输入人数"
                class="field-input"
              />
            </view>
          </view>
        </view>

        <!-- 性别选择 -->
        <view class="form-item">
          <view class="form-field gender-field">
            <view class="gender-buttons">
              <button
                class="gender-btn"
                :class="{ active: formData.gender === 'unlimited' }"
                @click="formData.gender = 'unlimited'"
              >
                不限
              </button>
              <button
                class="gender-btn"
                :class="{ active: formData.gender === 'female' }"
                @click="formData.gender = 'female'"
              >
                女
              </button>
              <button
                class="gender-btn"
                :class="{ active: formData.gender === 'male' }"
                @click="formData.gender = 'male'"
              >
                男
              </button>
            </view>
          </view>
        </view>

        <!-- 每人费用 -->
        <view class="form-item">
          <view class="form-field">
            <view class="field-label">每人费用</view>
            <view class="field-content">
              <text class="fee-amount">¥{{ formData.feePerPerson }}</text>
            </view>
          </view>
        </view>

        <!-- 开始时间 -->
        <view class="form-item">
          <wd-datetime-picker
            v-model="formData.startTime"
            type="datetime"
            :use-default-slot="true"
            @confirm="handleStartTimeConfirm"
          >
            <view class="form-field">
              <view class="field-label">*开始时间</view>
              <view class="field-content">
                <text class="field-value">
                  {{ formatDateTime(formData.startTime) || '请选择开始时间' }}
                </text>
                <wd-icon name="arrow-right" size="16px" color="#CCCCCC"></wd-icon>
              </view>
            </view>
          </wd-datetime-picker>
        </view>

        <!-- 报名结束时间 -->
        <view class="form-item">
          <wd-datetime-picker
            v-model="formData.endTime"
            type="datetime"
            :use-default-slot="true"
            @confirm="handleEndTimeConfirm"
          >
            <view class="form-field">
              <view class="field-label">*报名结束时间</view>
              <view class="field-content">
                <text class="field-value">
                  {{ formatDateTime(formData.endTime) || '请选择结束时间' }}
                </text>
                <wd-icon name="arrow-right" size="16px" color="#CCCCCC"></wd-icon>
              </view>
            </view>
          </wd-datetime-picker>
        </view>

        <!-- 标题 -->
        <view class="form-item">
          <view class="form-field title-field">
            <view class="field-label">*标题</view>
            <view class="field-content">
              <input
                type="text"
                v-model="formData.title"
                placeholder="请输入活动标题"
                class="field-input"
                maxlength="10"
              />
              <text class="char-count">{{ formData.title.length }}/10</text>
            </view>
          </view>
        </view>

        <!-- 活动详情描述 -->
        <view class="form-item">
          <view class="form-field description-field">
            <view class="field-label">活动详情描述</view>
            <view class="description-content">
              <textarea
                v-model="formData.description"
                placeholder="请输入活动详情描述"
                class="description-textarea"
                maxlength="1000"
              />
              <text class="char-count">{{ formData.description.length }}/1000</text>
            </view>
          </view>
        </view>

        <!-- 上传图片 -->
        <view class="form-item">
          <view class="form-field upload-field">
            <view class="field-label">上传图片</view>
            <view class="upload-area">
              <view
                v-for="(image, index) in displayImages"
                :key="index"
                class="upload-item"
                :class="{ 'add-btn': !image, uploaded: image }"
                @click="image ? handleDeleteImage(index) : handleUploadImage()"
              >
                <template v-if="!image">
                  <view class="upload-placeholder">
                    <wd-icon name="add" size="40px" color="#CCCCCC" class="upload-icon"></wd-icon>
                  </view>
                </template>
                <template v-else>
                  <image :src="image" mode="aspectFill" class="uploaded-image" />
                  <view class="delete-btn">
                    <wd-icon name="close" size="12px" color="#999"></wd-icon>
                  </view>
                </template>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部发布按钮 -->
      <view class="bottom-section">
        <button
          type="button"
          hover-class="button-hover"
          class="publish-btn"
          :disabled="!isFormValid"
          @click="handlePublish"
        >
          发布
        </button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useToast } from 'wot-design-uni'

const toast = useToast()

// 表单数据
const formData = reactive({
  event: '',
  carpoolType: 'find_people', // find_car: 人找车, find_people: 车找人
  startAddress: '',
  peopleCount: '',
  gender: 'male', // unlimited: 不限, female: 女, male: 男
  feePerPerson: '0.00',
  startTime: '',
  endTime: '',
  title: '',
  description: '',
  images: [] as string[],
})

// 赛事选项
const eventOptions = ref(['2024北京马拉松', '2024上海马拉松', '2024广州马拉松', '2024深圳马拉松'])

// 处理赛事选择
const handleEventConfirm = (event: any) => {
  formData.event = event.value
}

// 处理定位选择
const handleLocationSelect = () => {
  // 这里可以调用地图定位API
  uni.chooseLocation({
    success: (res) => {
      formData.startAddress = res.address
    },
    fail: (err) => {
      console.log('定位失败:', err)
    },
  })
}

// 处理开始时间确认
const handleStartTimeConfirm = (event: any) => {
  formData.startTime = event.value
}

// 处理结束时间确认
const handleEndTimeConfirm = (event: any) => {
  formData.endTime = event.value
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 显示的图片数组（包含空位）
const displayImages = computed(() => {
  const images = [...formData.images]
  // 最多显示3个位置
  while (images.length < 3) {
    images.push('')
  }
  return images.slice(0, 3)
})

// 处理图片上传
const handleUploadImage = () => {
  if (formData.images.length >= 3) {
    toast.error('最多只能上传3张图片')
    return
  }

  uni.chooseImage({
    count: 3 - formData.images.length,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      // 这里应该上传到服务器，现在先用本地路径
      const tempFilePaths = Array.isArray(res.tempFilePaths)
        ? res.tempFilePaths
        : [res.tempFilePaths]
      formData.images.push(...tempFilePaths)
    },
    fail: (err) => {
      console.log('选择图片失败:', err)
    },
  })
}

// 处理删除图片
const handleDeleteImage = (index: number) => {
  formData.images.splice(index, 1)
}

// 表单验证
const isFormValid = computed(() => {
  return (
    formData.event &&
    formData.startAddress &&
    formData.peopleCount &&
    formData.startTime &&
    formData.endTime &&
    formData.title
  )
})

// 处理发布
const handlePublish = async () => {
  if (!isFormValid.value) {
    toast.error('请完善所有必填信息')
    return
  }

  // 验证时间逻辑
  if (new Date(formData.endTime) >= new Date(formData.startTime)) {
    toast.error('报名结束时间必须早于开始时间')
    return
  }

  try {
    // 这里调用发布接口
    console.log('发布数据:', formData)
    toast.success('发布成功')

    // 发布成功后返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    toast.error('发布失败，请重试')
  }
}
</script>

<style lang="scss" scoped>
.carpool-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.page-container {
  padding: 40rpx 30rpx;
  background-color: white;
}

.form-section {
  margin-bottom: 60rpx;
}

.form-item {
  margin-bottom: 20rpx;
}

.form-field {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  background: #f5f5f5;
  border-radius: 16rpx;

  .field-label {
    margin-right: 28rpx;
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    white-space: nowrap;
  }

  .field-content {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;

    .field-value {
      flex: 1;
      font-size: 28rpx;
      color: #666;
      text-align: right;
    }

    .field-input {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      text-align: right;
      border: none;
      background: transparent;
      outline: none;

      &::placeholder {
        color: #999;
      }
    }

    .fee-amount {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
    }

    .char-count {
      margin-left: 20rpx;
      font-size: 24rpx;
      color: #999;
      white-space: nowrap;
    }
  }

  .location-btn {
    display: flex;
    align-items: center;
    margin-left: 20rpx;
    padding: 10rpx 20rpx;
    font-size: 24rpx;
    color: #666;
    background: #fff;
    border: 1rpx solid #ddd;
    border-radius: 8rpx;
  }

  .toggle-buttons {
    display: flex;
    gap: 20rpx;

    .toggle-btn {
      padding: 16rpx 32rpx;
      font-size: 28rpx;
      color: #666;
      background: #fff;
      border: 1rpx solid #ddd;
      border-radius: 8rpx;
      transition: all 0.3s ease;

      &.active {
        color: #fff;
        background: #c5f355;
        border-color: #c5f355;
      }
    }
  }
}

.gender-field {
  justify-content: center;

  .gender-buttons {
    display: flex;
    gap: 20rpx;

    .gender-btn {
      padding: 16rpx 32rpx;
      font-size: 28rpx;
      color: #666;
      background: #fff;
      border: 1rpx solid #ddd;
      border-radius: 8rpx;
      transition: all 0.3s ease;

      &.active {
        color: #fff;
        background: #c5f355;
        border-color: #c5f355;
      }
    }
  }
}

.title-field {
  .field-content {
    .field-input {
      text-align: left;
    }
  }
}

.description-field {
  flex-direction: column;
  align-items: flex-start;

  .field-label {
    margin-bottom: 20rpx;
    margin-right: 0;
  }

  .description-content {
    width: 100%;

    .description-textarea {
      width: 100%;
      min-height: 200rpx;
      padding: 20rpx;
      font-size: 28rpx;
      color: #333;
      background: #fff;
      border: 1rpx solid #ddd;
      border-radius: 8rpx;
      resize: none;
      outline: none;

      &::placeholder {
        color: #999;
      }
    }

    .char-count {
      display: block;
      margin-top: 10rpx;
      font-size: 24rpx;
      color: #999;
      text-align: right;
    }
  }
}

.upload-field {
  flex-direction: column;
  align-items: flex-start;

  .field-label {
    margin-bottom: 20rpx;
    margin-right: 0;
  }

  .upload-area {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
    width: 100%;
  }

  .upload-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 200rpx;
    height: 250rpx;
    background: white;
    border-radius: 16rpx;

    &.add-btn {
      border: 2rpx dashed #ddd;
      cursor: pointer;

      .upload-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
      }
    }

    &.uploaded {
      .uploaded-image {
        width: 100%;
        height: 100%;
        border-radius: 16rpx;
      }

      .delete-btn {
        position: absolute;
        top: -8rpx;
        right: -8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36rpx;
        height: 36rpx;
        background: #fff;
        border-radius: 50%;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        cursor: pointer;
      }
    }
  }
}

.bottom-section {
  padding: 30rpx;

  .publish-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 88rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #fff;
    background: linear-gradient(90deg, #c5f355, #a8e63a);
    border: none;
    border-radius: 44rpx;
    transition: all 0.3s ease;

    &[disabled] {
      color: #fff;
      background: #f1f1f1;
    }

    &:not([disabled]):active {
      opacity: 0.8;
    }
  }
}

.button-hover {
  opacity: 0.8;
}
</style>
